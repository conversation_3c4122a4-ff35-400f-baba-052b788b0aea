/* eslint-disable react/no-array-index-key */
import { AutocompleteProps, SxProps, Theme, useAutocomplete } from '@mui/material';
import Stack from '@mui/material/Stack';
import React, { useId, useState } from 'react';
import { convertToSelectOptions } from '@/lib/utils/array';
import { TOption } from '@/components/Molecules/RHFItems/RHFAutocomplete/RHFAutocomplete';
import Icon from '../Icon';
import { SearchIcon } from '../IconsComponent';
import { TTextFieldProps } from '../TextField/TextField.types';
import Tag from './Tag';
import {
  StyledFormHelperText,
  StyledInputWrapper,
  StyledLabel,
  StyledSearchButton,
} from './TreeViewAutoComplete.styled';
import TreeViewModal from './TreeViewModal';

export type TTreeViewAutoCompleteProps = {
  label?: string;
  searchTerm?: string;
  selectTerm?: string;
  name: string;
  options?: TOption[];
  textfieldProps?: TTextFieldProps;
  showValue?: boolean;
  defaultValue?: TOption[];
  sxLabel?: SxProps<Theme>;
  sxContainer?: SxProps<Theme>;
  isHideDeleteWorker?: boolean;
  error?: boolean;
  helperText?: string;
  showSelectedValue?: boolean;
  handleOnChange?: (value: (string | TOption)[]) => void;
} & Partial<AutocompleteProps<TOption, any, any, any>>;

export const TreeViewAutoComplete: React.FC<TTreeViewAutoCompleteProps> = ({
  value,
  options = [],
  sxContainer,
  helperText,
  handleOnChange,
  label = '',
  searchTerm = 'items',
  selectTerm = 'items',
  showSelectedValue = true,
  disabled = false,
}) => {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const { getInputProps, focused, setAnchorEl } = useAutocomplete<TOption, any, any, any>({
    id,
    multiple: true,
    options,
    getOptionLabel: option => {
      if (typeof option === 'string') return option;
      // @ts-ignore
      return option?.name || option?.displayName || '';
    },
  });
  const onDelete = (index: number) => {
    if (Array.isArray(value) && handleOnChange && index >= 0 && index < value.length) {
      const newValue = [...value.slice(0, index), ...value.slice(index + 1)];
      handleOnChange(newValue);
    }
  };

  const onOpenModal = () => {
    if (disabled) return;
    setOpen(true);
  };
  return (
    <>
      <Stack sx={{ ...sxContainer, height: '100%' }}>
        {label && <StyledLabel variant="heading-medium-700">{label}</StyledLabel>}
        <StyledInputWrapper ref={setAnchorEl} $disabled={disabled} className={focused ? 'focused' : ''}>
          <Stack
            sx={theme => ({
              flex: 1,
              flexDirection: 'row',
              flexWrap: 'wrap',
              alignItems: 'center',
              gap: theme.spacing(0.5),
            })}
          >
            {showSelectedValue &&
              (Array.isArray(value) ? value : [])?.map((option: TOption | string, index: number) => {
                const label = typeof option === 'string' ? option : option?.name;
                const isDisabledTag = typeof option !== 'string' && option?.disabled;
                return (
                  <Tag
                    key={`${label}-${index}`}
                    disabled={isDisabledTag}
                    label={label}
                    onDelete={() => {
                      if (isDisabledTag) return;
                      onDelete(index);
                    }}
                  />
                );
              })}
            <input {...getInputProps()} disabled={disabled} onClick={onOpenModal} />
          </Stack>
          <StyledSearchButton
            disabled={disabled}
            disableRipple
            disableFocusRipple
            disableTouchRipple
            onClick={onOpenModal}
          >
            <SearchIcon />
          </StyledSearchButton>
        </StyledInputWrapper>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" gap={0.5} mt={0.5} minHeight="20px">
          {helperText && (
            <>
              <Icon variant="warning" size={1.5} />
              <StyledFormHelperText>{helperText}</StyledFormHelperText>
            </>
          )}
        </Stack>
      </Stack>
      <TreeViewModal
        options={options}
        selectedOptions={Array.isArray(value) ? value.map(item => (typeof item === 'string' ? item : item?.id)) : []}
        onSelect={value => {
          if (handleOnChange)
            handleOnChange(
              convertToSelectOptions(options)
                ?.filter(
                  option =>
                    value.includes(option?.id) && !Array.isArray(option?.children) && !Array.isArray(option?.items)
                )
                ?.map(option => ({
                  id: option.id,
                  name: option.name,
                })) || []
            );
          setOpen(false);
        }}
        isOpen={open}
        onCancel={() => setOpen(false)}
        searchTerm={searchTerm}
        selectTerm={selectTerm}
      />
    </>
  );
};
