import { Theme } from '@mui/material';
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import { dialogClasses } from '@mui/material/Dialog';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import { textFieldClasses } from '@mui/material/TextField';
import React, { useEffect, useMemo, useState } from 'react';
import Search from '@/components/Atoms/Search';
import Modal from '@/components/Atoms/Modal';
import { TreeViewMultiple } from '@/components/Atoms/TreeViewMultiple/TreeViewMultiple';
import { StyledButton, StyledButtonWrapper } from './TreeViewModal.styled';
import { ITreeViewOption } from '../../TreeViewMultiple/TreeViewMultiple.types';
import { CATGEORY_NAMES } from '@/lib/constants/category';
import { convertToSelectOptions } from '@/lib/utils/array';

export type TTreeViewModalProps = {
  isOpen: boolean;
  searchTerm: string;
  selectTerm: string;
  isLoading?: boolean;
  options?: ITreeViewOption[];
  selectedOptions?: string[];
  onSelect: (value: string[]) => void;
  onCancel: () => void;
};

export const TreeViewModal: React.FC<TTreeViewModalProps> = ({
  isOpen = false,
  searchTerm,
  selectTerm,
  isLoading,
  options,
  selectedOptions = [],
  onSelect,
  onCancel,
}) => {
  const [pendingSelectedOptions, setPendingSelectedOptions] = useState<string[]>([]);
  const [selectedOption, setSelectedOption] = useState<ITreeViewOption>();
  const [keyword, setKeyword] = useState<string>('');
  useEffect(() => {
    setPendingSelectedOptions([...selectedOptions]);
  }, [JSON.stringify(selectedOptions)]);
  useEffect(() => {
    const temp = options?.find(option => !option?.parent);
    if (temp) {
      setSelectedOption(temp);
    }
  }, [JSON.stringify(options)]);

  const filteredOptions = useMemo(() => {
    if (!keyword) {
      return selectedOption?.children || options || [];
    }
    const lowercasedKeyword = keyword.toLowerCase();

    const filter = (items: ITreeViewOption[]): ITreeViewOption[] =>
      items.reduce((acc, item) => {
        if (typeof item.name === 'string' && item.name.toLowerCase().includes(lowercasedKeyword)) {
          acc.push(item);
          return acc;
        }
        if (item.children) {
          const filteredChildren = filter(item.children);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        }
        return acc;
      }, [] as ITreeViewOption[]);

    return filter(selectedOption?.children || options || []);
  }, [keyword, options, selectedOption]);

  const count = useMemo(
    () =>
      (
        convertToSelectOptions(options || [])
          ?.filter(
            option =>
              pendingSelectedOptions.includes(option?.id) &&
              !Array.isArray(option?.children) &&
              !Array.isArray(option?.items)
          )
          ?.map(option => option?.id) || []
      ).length,
    [JSON.stringify(options), JSON.stringify(pendingSelectedOptions)]
  );
  return (
    <Modal
      isOpen={isOpen}
      handleClose={onCancel}
      dialogProps={{
        sx: (theme: Theme) => ({
          [`& .${dialogClasses.paper}`]: {
            height: 'fit-content',
            padding: theme.spacing(3),
            maxHeight: 'calc(100% - 48px)',
            borderRadius: theme.spacing(1.25),
            [theme.breakpoints.down('md')]: {
              padding: theme.spacing(2),
            },
          },
        }),
      }}
    >
      <Stack
        gap={3}
        sx={(theme: Theme) => ({
          width: theme.spacing(126.5),
          [theme.breakpoints.down('md')]: {
            width: 'auto',
          },
        })}
      >
        <Search
          onSearch={setKeyword}
          placeholder={`Search ${searchTerm}`}
          options={[]}
          textFieldProps={{
            sx: theme => ({
              [`&.${textFieldClasses.root} .${outlinedInputClasses.root}`]: {
                border: `1px solid ${theme.palette.neutrals.N40.main}`,
                [`&.${outlinedInputClasses.focused}`]: {
                  border: `1px solid ${theme.palette.neutrals.N50.main}`,
                },
                '&:hover': {
                  border: `1px solid ${theme.palette.neutrals.N50.main}`,
                },
              },
            }),
          }}
        />
        {options?.find(option => !option?.parent) && (
          <Grid container spacing={1.5}>
            {options?.map(option => (
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Box
                  sx={theme => ({
                    width: '100%',
                    padding: theme.spacing(1.5),
                    borderRadius: theme.spacing(1.25),
                    color:
                      selectedOption?.id === option?.id ? theme.palette.common.white : theme.palette.neutrals.N50.main,
                    border: `1px solid ${
                      selectedOption?.id === option?.id ? 'transparent' : theme.palette.neutrals.N180.main
                    }`,
                    backgroundColor:
                      selectedOption?.id === option?.id ? theme.palette.neutrals.N50.main : theme.palette.common.white,
                    cursor: 'pointer',
                    textTransform: 'capitalize',
                    ...theme.typography['heading-medium-700'],
                  })}
                  onClick={() => setSelectedOption(option)}
                >
                  {typeof option?.name === 'string' && CATGEORY_NAMES?.[option?.name]
                    ? CATGEORY_NAMES?.[option?.name]
                    : option?.name}
                </Box>
              </Grid>
            ))}
          </Grid>
        )}
        <TreeViewMultiple
          wrapperProps={{
            sx: {
              maxHeight: '50vh',
              overflowY: 'auto',
            },
          }}
          options={filteredOptions}
          selected={pendingSelectedOptions}
          onSelectedChange={value => setPendingSelectedOptions(value)}
        />
        <StyledButtonWrapper>
          <StyledButton
            variant="outlined"
            label="Cancel"
            size="medium"
            isLoading={isLoading}
            loadingText="Cancel"
            onClick={onCancel}
          />
          <StyledButton
            variant="contained"
            label={`Select ${count} ${
              pendingSelectedOptions.length === 1 ? selectTerm.replace(/(s|s)$/, '') : selectTerm
            }`}
            size="medium"
            isLoading={isLoading}
            loadingText={`Selecting ${count} ${selectTerm}`}
            onClick={() => onSelect(pendingSelectedOptions)}
          />
        </StyledButtonWrapper>
      </Stack>
    </Modal>
  );
};
