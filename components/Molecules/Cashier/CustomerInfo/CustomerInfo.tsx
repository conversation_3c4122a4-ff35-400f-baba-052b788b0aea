import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import React, { useCallback, useContext, useMemo, useState } from 'react';
import Icon from '@/components/Atoms/Icon/Icon';
import { TextField } from '@/components/Atoms/TextField/TextField';
import Typography from '@/components/Atoms/Typography';
import { CustomerProfileModal } from '@/components/Organisms/Cashier/CustomerProfileModal/CustomerProfileModal';
import { FORMAT_DD_MM_YYYY_SLASH } from '@/lib/constants/dateTime';
import { PaymentContext } from '@/lib/context/PaymentContext';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { TCustomerCoupon } from '@/lib/types/entities/coupon';
import { TCustomer } from '@/lib/types/entities/customer';
import { TOrderProductVariants } from '@/lib/types/entities/order';
import { getCredits } from '@/lib/utils/credit';
import { concatenateNames, round2decimal } from '@/lib/utils/string';
import { TItemCashier } from '../CashierSearch/CashierSearch';
import ComboCouponModal from '../ComboCouponModal';
import { MembershipRefundPurgedModal } from '../MembershipRefundPurgedModal/MembershipRefundPurgedModal';
import {
  StyledAvatar,
  StyledButton,
  StyledContainer,
  StyledContainerWrapper,
  StyledCustomerInfo,
  StyledTitle,
} from './CustomerInfo.styled';

dayjs.extend(utc);
dayjs.extend(timezone);

type TCustomerInfo = {
  rfid?: string;
  customerInfo?: Partial<TCustomer>;
  showInfo: boolean;
  onItemSelect: (item: TItemCashier) => void;
  onCloseClick: () => void;
  onRecieveRFID: (value: string) => void;
};
const getUnusedCode = (availableCodes: string[], usedComboCodes: string[]) => {
  for (let i = 0; i < availableCodes.length; i + 1) {
    const code = availableCodes[i];
    if (!usedComboCodes.includes(code)) {
      return code;
    }
  }

  return null;
};

export const CustomerInfo: React.FC<TCustomerInfo> = ({
  showInfo,
  customerInfo,
  onRecieveRFID,
  onCloseClick,
  rfid,
  onItemSelect,
}) => {
  if (showInfo) {
    const { usedComboCodes, setUsedComboCodes } = useContext(PaymentContext);
    const [isOpenModal, setIsOpenModal] = useState<boolean>(false);
    const [isOpenMembershipRefundPurgedModal, setIsOpenMembershipRefundPurgedModal] = useState<boolean>(false);
    const [isOpenUseComboCouponModal, setIsOpenUseComboCouponModal] = useState<boolean>(false);
    const { showError } = useAlert();
    const customerName = concatenateNames(customerInfo?.firstName || '', customerInfo?.lastName || '');
    const { newCredit: credit, oldCredit } = useMemo(() => {
      const { newCredit, oldCredit } = getCredits(customerInfo?.credits || []);
      return { newCredit, oldCredit };
    }, [customerInfo?.credits]);

    const onUseComboCoupon = (coupon: TCustomerCoupon) => {
      const outOfCoupon = Number(coupon.use) > coupon?.codes?.length || !coupon?.codes?.length;
      if (outOfCoupon) return showError({ title: 'Not enough discount codes to use' });
      const availableCodes = coupon?.codes || [];

      if (Array.isArray(usedComboCodes)) {
        const code = getUnusedCode(availableCodes, usedComboCodes);
        if (code) {
          setUsedComboCodes([...usedComboCodes, code]);
          onItemSelect({
            id: coupon?.id,
            name: coupon?.couponName,
            credit: 0,
            type: 'coupon' as TOrderProductVariants,
            price: 0,
            remain: availableCodes.length,
            avatar: {
              id: '',
              url: coupon?.avatar || '',
            },
            couponCode: code,
          });
        }
      }

      setIsOpenUseComboCouponModal(false);
    };
    return (
      <>
        <StyledContainerWrapper>
          <Stack gap={0.5}>
            <StyledContainer>
              <StyledAvatar src={customerInfo?.avatar?.url} onClick={() => setIsOpenModal(true)}>
                {customerName?.[0]}
              </StyledAvatar>
              <Stack gap="4px" onClick={() => setIsOpenModal(true)} sx={{ cursor: 'pointer' }}>
                <Typography variant="heading-xmedium-700">{customerName}</Typography>
                {customerInfo?.gender?.name && (
                  <StyledCustomerInfo>
                    <Typography variant="heading-small-700" minWidth="115px">
                      Gender
                    </Typography>
                    <StyledTitle variant="body-large-400">{customerInfo?.gender?.name}</StyledTitle>
                  </StyledCustomerInfo>
                )}
                {customerInfo?.membershipNo && (
                  <StyledCustomerInfo sx={{ alignItems: 'flex-start !important' }}>
                    <Typography variant="heading-small-700" minWidth="115px">
                      Membership No.
                    </Typography>
                    <StyledTitle variant="body-large-400" width="70px" sx={{ wordBreak: 'break-word' }}>
                      {customerInfo?.membershipNo}
                    </StyledTitle>
                  </StyledCustomerInfo>
                )}
              </Stack>
              <Stack sx={{ flexGrow: 1, justifyContent: 'flex-end', alignItems: 'flex-end', cursor: 'pointer' }}>
                <Icon variant="close" onClick={onCloseClick} />
              </Stack>
            </StyledContainer>
            {(credit?.isShow || oldCredit?.isShow || customerInfo?.remark) && (
              <Divider
                sx={theme => ({
                  borderBottomWidth: theme.spacing(1 / 8),
                  opacity: 1,
                  margin: theme.spacing(1, 0),
                  borderColor: theme.palette.neutrals.N40.main,
                })}
              />
            )}
            {credit?.isShow && (
              <StyledCustomerInfo>
                <Stack flexDirection="row" gap={1.5} alignItems="center">
                  <Typography component="p" variant="heading-small-700" minWidth="70px">
                    Credit
                  </Typography>
                  <StyledTitle width="100%" variant="body-large-400" textAlign="left">{`${round2decimal(
                    credit?.balance || 0
                  )}`}</StyledTitle>
                </Stack>
                <Stack flexDirection="row" gap={1.5} alignItems="center">
                  <Typography component="p" variant="body-small-400" minWidth="70px">
                    {dayjs.utc(credit?.rawExpiredDate).format(FORMAT_DD_MM_YYYY_SLASH)}
                  </Typography>
                  <Typography
                    variant="body-large-400"
                    color="white"
                    sx={{
                      background: credit?.isExpired ? 'red' : '#3BA716',
                      borderRadius: '10px',
                      padding: '4px 10px',
                    }}
                  >
                    {credit?.isExpired ? 'Expired' : 'Valid'}
                  </Typography>
                </Stack>
              </StyledCustomerInfo>
            )}
            {oldCredit?.isShow && (
              <StyledCustomerInfo>
                <Stack flexDirection="row" gap={1.5} alignItems="center">
                  <Typography component="p" variant="heading-small-700" minWidth="70px">
                    Old credit
                  </Typography>
                  <StyledTitle width="100%" variant="body-large-400" textAlign="left">{`${round2decimal(
                    oldCredit?.balance || 0
                  )}`}</StyledTitle>
                </Stack>
                <Stack flexDirection="row" gap={1.5} alignItems="center">
                  <Typography component="p" variant="body-small-400" minWidth="70px">
                    {dayjs.utc(oldCredit?.rawExpiredDate).format(FORMAT_DD_MM_YYYY_SLASH)}
                  </Typography>
                  <Typography
                    variant="body-large-400"
                    color="white"
                    sx={{
                      background: oldCredit?.isExpired ? 'red' : '#3BA716',
                      borderRadius: '10px',
                      padding: '4px 10px',
                    }}
                  >
                    {oldCredit?.isExpired ? 'Expired' : 'Valid'}
                  </Typography>
                </Stack>
              </StyledCustomerInfo>
            )}
            {customerInfo?.expiryDate && (
              <StyledCustomerInfo>
                <Stack flexDirection="row" gap={1.5} alignItems="center">
                  <Typography component="p" variant="heading-small-700" minWidth="70px">
                    Passport
                  </Typography>
                </Stack>
                <Stack flexDirection="row" gap={1.5} alignItems="center">
                  <Typography component="p" variant="body-small-400" minWidth="70px">
                    {customerInfo?.expiryDate && dayjs(customerInfo?.expiryDate)?.isValid()
                      ? dayjs.utc(customerInfo?.expiryDate).format(FORMAT_DD_MM_YYYY_SLASH)
                      : FORMAT_DD_MM_YYYY_SLASH}
                  </Typography>
                  <Typography
                    variant="body-large-400"
                    color="white"
                    sx={{
                      background: oldCredit?.isExpired ? 'red' : '#3BA716',
                      borderRadius: '10px',
                      padding: '4px 10px',
                      visibility: 'hidden',
                    }}
                  >
                    {oldCredit?.isExpired ? 'Expired' : 'Valid'}
                  </Typography>
                </Stack>
              </StyledCustomerInfo>
            )}
            {customerInfo?.remark && (
              <StyledCustomerInfo>
                <StyledTitle
                  variant="heading-small-700"
                  sx={{
                    width: '70px !important',
                    minWidth: '70px !important',
                  }}
                >
                  Remark
                </StyledTitle>
                <StyledTitle
                  variant="body-large-400"
                  maxHeight="34px"
                  maxWidth="300px"
                  sx={{
                    flex: 1,
                    wordBreak: 'break-word',
                    '&::-webkit-scrollbar': {
                      background: '#DCE5F2',
                      borderRadius: '5px',
                      width: '5px',
                    },
                    '&:: -webkit-scrollbar-thumb': {
                      background: '#99A3B4',
                      borderRadius: '5px',
                    },
                  }}
                  overflow="auto"
                >{`${customerInfo?.remark || ''}`}</StyledTitle>
              </StyledCustomerInfo>
            )}
          </Stack>
          <Stack flexDirection="row" justifyContent="space-between" alignItems="center" gap={1.5}>
            <StyledButton variant="contained" label="Use Coupon" onClick={() => setIsOpenUseComboCouponModal(true)} />
            <StyledButton
              variant="contained"
              label="Purged"
              onClick={() => setIsOpenMembershipRefundPurgedModal(true)}
            />
          </Stack>
        </StyledContainerWrapper>
        <CustomerProfileModal isOpen={isOpenModal} customer={customerInfo} handleClose={() => setIsOpenModal(false)} />
        <ComboCouponModal
          customerId={customerInfo?.id || ''}
          usedComboCodes={usedComboCodes || []}
          isOpen={isOpenUseComboCouponModal}
          onUse={onUseComboCoupon}
          onClose={() => setIsOpenUseComboCouponModal(false)}
          // userCoupon: TCustomerCoupon[];
        />
        <MembershipRefundPurgedModal
          customerId={customerInfo?.id || ''}
          isOpen={isOpenMembershipRefundPurgedModal}
          handleClose={() => setIsOpenMembershipRefundPurgedModal(false)}
        />
      </>
    );
  }

  return (
    <StyledContainer>
      <StyledCustomerInfo padding={1}>
        <StyledTitle variant="heading-medium-700" minWidth="50px">
          RFID
        </StyledTitle>
        <TextField
          value={rfid}
          autoFocus
          onChange={e => onRecieveRFID(e?.target?.value)}
          formControlProps={{
            sx: { flex: 1 },
          }}
        />
      </StyledCustomerInfo>
    </StyledContainer>
  );
};
