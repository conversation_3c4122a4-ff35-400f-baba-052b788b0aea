{"name": "botg-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "next": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "build:standalone": "next build && cp -r .next/static .next/standalone/.next/ && cp -r public .next/standalone/", "start-standalone": "PORT=3000 HOSTNAME=0.0.0.0 node server.js", "checkFormat": "tsc --noEmit", "test:coverage": "jest --coverage", "lint:staged": "lint-staged", "postinstall": "husky install", "eslint": "eslint \"**/*.+(js|jsx|ts|tsx)\"", "eslint:fix": "eslint --fix \"**/*.+(js|jsx|ts|tsx)\"", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@emotion/css": "^11.11.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.8", "@formatjs/intl-localematcher": "^0.4.2", "@hookform/resolvers": "^3.3.1", "@mui/material": "^5.14.11", "@mui/x-date-pickers": "^6.3.1", "@mui/x-tree-view": "^6.17.0", "@nestjsx/crud-request": "^5.0.0-alpha.3", "@reduxjs/toolkit": "^1.9.6", "@testing-library/jest-dom": "^6.1.3", "@testing-library/react": "^14.0.0", "@types/node": "20.6.3", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "18.2.7", "axios": "^1.6.2", "clsx": "^2.0.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "eslint-config-next": "13.5.2", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "formik": "^2.4.5", "framer-motion": "^10.16.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "negotiator": "^0.6.3", "next": "^14.0.1", "next-auth": "^4.24.3", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.8.5", "react-bootstrap": "^2.9.0", "react-color": "^2.19.3", "react-contenteditable": "^3.3.7", "react-cropper": "^2.3.3", "react-dom": "18.2.0", "react-easy-crop": "^5.0.7", "react-hook-form": "^7.47.0", "react-inlinesvg": "^4.0.4", "react-qr-code": "^2.0.12", "react-redux": "^8.1.2", "react-to-print": "^2.14.15", "react-zoom-pan-pinch": "^3.3.0", "server-only": "^0.0.1", "sweetalert2": "^11.7.31", "swr": "^2.2.4", "tsconfig-paths-webpack-plugin": "^4.1.0", "yup": "^1.3.1", "zod": "^3.22.4", "@netlify/plugin-nextjs": "*"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@storybook/addon-essentials": "^7.4.5", "@storybook/addon-interactions": "^7.4.5", "@storybook/addon-links": "^7.4.5", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.4.5", "@storybook/nextjs": "^7.4.5", "@storybook/react": "^7.4.5", "@storybook/testing-library": "^0.2.1", "@types/bootstrap": "^5.2.7", "@types/jest": "^29.5.5", "@types/negotiator": "^0.6.1", "@types/react": "^18.2.31", "@types/react-big-calendar": "^1.8.5", "@types/react-color": "^3.0.10", "@typescript-eslint/eslint-plugin": "^6.7.3", "@typescript-eslint/parser": "^6.7.3", "eslint": "^8.50.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-storybook": "^0.6.14", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "3.0.3", "storybook": "^7.4.5", "typescript": "^5.2.2"}, "engines": {"npm": ">=9.x", "node": ">=18.x"}, "packageManager": "yarn@1.22.19+sha1.4ba7fc5c6e704fce2066ecbfb0b0d8976fe62447"}